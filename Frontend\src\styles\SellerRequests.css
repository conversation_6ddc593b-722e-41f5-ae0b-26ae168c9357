/* SellerRequests Component Styles - Consolidated from SellerRequests.css and SellerRequestsStandardized.css */
/* Scoped to seller-requests-container to avoid global conflicts */
.seller-requests-container {
  padding: 0;
  background-color: transparent;
  font-family: "Poppins", sans-serif;
}

/* Standardized Table Styling - Following AdminCMSPages pattern */
.seller-requests-container .requests-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--smallfont);
  background-color: var(--white);
  margin: 0;
}

.seller-requests-container .requests-table th {
  padding: var(--basefont);
  text-align: left;
  vertical-align: middle;
  background-color: var(--bg-gray);
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--smallfont);
  border-bottom: 1px solid var(--light-gray);
  white-space: nowrap;
}

.seller-requests-container .requests-table td {
  padding: var(--basefont);
  text-align: left;
  vertical-align: middle;
  border-bottom: 1px solid var(--light-gray);
  font-size: var(--smallfont);
  color: var(--text-color);
}

.seller-requests-container .requests-table tbody tr:last-child td {
  border-bottom: none;
}

.seller-requests-container .video-doc {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.seller-requests-container .video-doc img {
  width: 55px;
  height: 55px;
  border-radius: var(--border-radius);
  object-fit: cover;
}

.seller-requests-container .video-doc span {
  font-size: var(--smallfont);
  font-weight: 500;
  color: var(--text-color);
}

/* Action icons - Following consistent action button pattern */
.seller-requests-container .action-icons {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

.seller-requests-container .eye-icon,
.seller-requests-container .comment-icon {
  font-size: var(--basefont);
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius);
}

.seller-requests-container .action-icon:hover,
.seller-requests-container .eye-icon:hover,
.seller-requests-container .comment-icon:hover {
  color: var(--primary-color);
  background-color: var(--primary-light-color);
  transform: scale(1.05);
}

/* Header Section - Enhanced from Standardized version */
.seller-requests-container .requests-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--heading6) 0;
  margin-bottom: var(--basefont);
  flex-direction: column-reverse;
}

.seller-requests-container .requests-header h2 {
  margin: 0;
  font-size: var(--heading4);
  color: var(--secondary-color);
  font-weight: 600;
}

/* Enhanced Requests Header - Following consistent dashboard pattern */
.seller-requests-container .requests-header.enhanced {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--heading4);
  gap: var(--heading5);
  padding: var(--basefont);
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
}

/* Stats Section - Consolidated from both versions */
.seller-requests-container .requests-stats {
  display: flex;
  gap: var(--basefont);
  margin-bottom: var(--heading6);
  flex-wrap: wrap;
  width: 100%;
}

.seller-requests-container .stat-item {
  display: flex;
gap: var(--basefont);
  align-items: center;
  padding: 5px var(--basefont);
  background: var(--white);
  border-radius: var(--border-radius-large);
  min-width: 150px;
  box-shadow: var(--box-shadow-light);
  border: 1px solid var(--light-gray);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  justify-content: center;
}
.seller-requests-container .stat-contentcss{
  display: grid;
  justify-items: center;
    gap: 05px;
}
.seller-requests-container .stat-item:hover {
  transform: scale(1.02);
  box-shadow: var(--box-shadow);
}

.seller-requests-container .stat-icon {
  font-size: var(--heading3);
  
  color: var(--primary-color);
}

.seller-requests-container .stat-count {
  font-size: var(--heading4);
  font-weight: 700;
  color: var(--secondary-color);

}

.seller-requests-container .stat-label {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  text-align: center;
  font-weight: 500;
}

/* Filters Section - Enhanced from Standardized version */
.seller-requests-container .requests-filters {
  display: flex;
  gap: var(--basefont);
  align-items: center;
  padding: var(--basefont);
  background: var(--white);
  border-radius: var(--border-radius-large);
  margin-bottom: var(--heading6);
  box-shadow: var(--box-shadow-light);
  border: 1px solid var(--light-gray);
  flex-wrap: wrap;
  width: 100%;
}

.seller-requests-container .filter-group {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.seller-requests-container .filter-group label {
  font-size: var(--smallfont);
  color: var(--secondary-color);
  font-weight: 500;
}

.seller-requests-container .search-group {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.seller-requests-container .filter-select,
.seller-requests-container .search-input {
  padding: var(--smallfont) var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  background: var(--white);
  color: var(--text-color);
  transition: all 0.3s ease;
}

.seller-requests-container .filter-select:focus,
.seller-requests-container .search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(236, 29, 59, 0.1);
}

.seller-requests-container .search-input {
  min-width: 200px;
}

/* Standardized Table Container with Scrolling */
.seller-requests-container .requests-table-container {
  background: var(--white);

  overflow-x: auto;
  box-shadow: var(--box-shadow-light);

}

/* Request Details Cell - Enhanced from Standardized version */
.seller-requests-container .request-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.seller-requests-container .request-title {
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--smallfont);
}

.seller-requests-container .request-meta {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
}

.seller-requests-container .content-type,
.seller-requests-container .sport {
  font-size: var(--extrasmallfont);
  padding: 2px var(--smallfont);
  border-radius: var(--border-radius);
  background: var(--bg-gray);
  color: var(--dark-gray);
}

/* Buyer Info Cell - Enhanced from Standardized version */
.seller-requests-container .buyer-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.seller-requests-container .buyer-name {
  font-weight: 500;
  color: var(--secondary-color);
  font-size: var(--smallfont);
}

.seller-requests-container .buyer-email {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

/* Budget Cell - Enhanced from Standardized version */
.seller-requests-container .budget-info {
  display: flex;
  align-items: center;
}

.seller-requests-container .budget-amount {
  font-weight: 600;
  color: var(--primary-color);
  font-size: var(--smallfont);
}

/* Date Cell */
.seller-requests-container .date-info {
  font-size: var(--smallfont);
  color: var(--text-color);
}

/* Status Cell - Enhanced from Standardized version */
.seller-requests-container .status-badge {
  display: flex;
  padding: 4px var(--smallfont);
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: capitalize;
  align-items: center;
  gap: var(--extrasmallfont);
}

.seller-requests-container .status-badge.pending {
  background-color: #fef3c7;
  color: #92400e;
}

.seller-requests-container .status-badge.in-progress {
  background-color: #dbeafe;
  color: #1e40af;
}

.seller-requests-container .status-badge.completed {
  background-color: #dcfce7;
  color: #166534;
}

.seller-requests-container .status-badge.cancelled {
  background-color: #fee2e2;
  color: #dc2626;
}

/* Legacy status classes for backward compatibility */
.seller-requests-container .status-orange {
  background: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.seller-requests-container .status-green {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.seller-requests-container .status-red {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.seller-requests-container .status-blue {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.seller-requests-container .status-purple {
  background: rgba(155, 89, 182, 0.1);
  color: #9b59b6;
}

.seller-requests-container .status-gray {
  background: var(--bg-gray);
  color: var(--dark-gray);
}

/* Action Buttons - Standardized Pattern */
.seller-requests-container .action-buttons {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
  justify-content: center;
}

.seller-requests-container .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: transparent;
  color: var(--text-color);
}

.seller-requests-container .view-btn {

    background-color: transparent;
    font-size: var(--heading6);
  color: var(--text-color);
}

.seller-requests-container .view-btn:hover {
  
  transform: scale(1.05);
}

.seller-requests-container .respond-btn svg {
  background: transparent;
  color: var(--text-color);
  font-weight: 700;
}

.seller-requests-container .respond-btn:hover {

  transform: scale(1.05);
}

.seller-requests-container .payment-btn {
  background: #fef3c7;
  color: #92400e;
}

.seller-requests-container .payment-btn:hover {
  background: #f39c12;
  color: var(--white);
  transform: scale(1.05);
}

/* Empty State - Enhanced from Standardized version */
.seller-requests-container .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading3);
  text-align: center;
  color: var(--dark-gray);
}

.seller-requests-container .empty-icon {
  font-size: var(--heading2);
  color: var(--light-gray);
  margin-bottom: var(--basefont);
}

.seller-requests-container .empty-state h3 {
  margin-bottom: var(--smallfont);
  color: var(--secondary-color);
  font-size: var(--heading6);
}

.seller-requests-container .empty-state p {
  color: var(--dark-gray);
  font-size: var(--smallfont);
  max-width: 400px;
}

/* Responsive Design - Following consistent responsive pattern */
@media (max-width: 1024px) {
  .seller-requests-container .requests-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--basefont);
  }

  .seller-requests-container .requests-filters {
    justify-content: space-between;
    gap: var(--smallfont);
  }
}

@media (max-width: 768px) {
  .seller-requests-container .requests-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--basefont);
  }

  .seller-requests-container .requests-filters {
    flex-direction: column;
    align-items: stretch;
    gap: var(--basefont);
    padding: var(--smallfont);
  }

  .seller-requests-container .filter-group {
    justify-content: space-between;
  }

  .seller-requests-container .search-input {
    min-width: auto;
    width: 100%;
  }

  .seller-requests-container .requests-stats {
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--smallfont);
  }

  .seller-requests-container .stat-item {
    min-width: 80px;
    padding: var(--smallfont);
  }

  .seller-requests-container .requests-table {
    font-size: var(--extrasmallfont);
  }

  .seller-requests-container .requests-table th,
  .seller-requests-container .requests-table td {
    padding: var(--smallfont);
  }

  .seller-requests-container .action-buttons {
    gap: 2px;
  }

  .seller-requests-container .action-btn {
    width: 28px;
    height: 28px;
    font-size: var(--extrasmallfont);
  }
}

@media (max-width: 480px) {
  .seller-requests-container .requests-table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .seller-requests-container .requests-table {
    min-width: 600px;
  }

  .seller-requests-container .requests-header,
  .seller-requests-container .requests-filters {
    padding: var(--smallfont);
  }

  .seller-requests-container .stat-item {
    min-width: 70px;
    padding: var(--smallfont);
  }

  .seller-requests-container .stat-count {
    font-size: var(--heading5);
  }

  .seller-requests-container .empty-state {
    padding: var(--heading5);
  }
}
